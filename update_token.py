#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import base64
from datetime import datetime
from simple_temu_client import Simple<PERSON>emu


def extract_token_from_curl():
    """从curl命令中提取token"""
    print("=== Token 更新工具 ===\n")
    print("请按以下步骤获取新的token:")
    print("1. 打开浏览器，登录到 https://agentseller-us.temu.com")
    print("2. 打开开发者工具 (F12)")
    print("3. 切换到 Network 标签页")
    print("4. 刷新页面或执行任何操作")
    print("5. 找到任意API请求 (通常是POST请求)")
    print("6. 右键点击请求 -> Copy -> Copy as cURL")
    print("7. 将cURL命令粘贴到下面\n")
    
    curl_command = input("请粘贴cURL命令: ").strip()
    
    if not curl_command:
        print("未输入cURL命令")
        return None
    
    # 从curl命令中提取seller_temp
    try:
        # 查找Cookie头
        if "Cookie:" in curl_command or "-H 'Cookie:" in curl_command:
            # 提取Cookie部分
            cookie_start = curl_command.find("Cookie:")
            if cookie_start == -1:
                cookie_start = curl_command.find("'Cookie:")
            
            if cookie_start != -1:
                cookie_end = curl_command.find("'", cookie_start + 8)
                if cookie_end == -1:
                    cookie_end = curl_command.find('"', cookie_start + 8)
                
                if cookie_end != -1:
                    cookie_str = curl_command[cookie_start:cookie_end]
                    
                    # 查找seller_temp
                    seller_temp_start = cookie_str.find("seller_temp=")
                    if seller_temp_start != -1:
                        seller_temp_start += 12  # len("seller_temp=")
                        seller_temp_end = cookie_str.find(";", seller_temp_start)
                        if seller_temp_end == -1:
                            seller_temp_end = len(cookie_str)
                        
                        seller_temp = cookie_str[seller_temp_start:seller_temp_end]
                        return seller_temp
        
        print("未在cURL命令中找到seller_temp")
        return None
        
    except Exception as e:
        print(f"解析cURL命令失败: {e}")
        return None


def manual_input_token():
    """手动输入token"""
    print("\n=== 手动输入Token ===")
    print("请直接输入seller_temp的值:")
    print("(通常以 'N_' 开头)")
    
    seller_temp = input("seller_temp: ").strip()
    
    if not seller_temp:
        print("未输入token")
        return None
    
    return seller_temp


def update_all_cookies():
    """更新所有cookies"""
    print("\n=== 批量更新Cookies ===")
    print("请输入完整的Cookie字符串:")
    print("(从浏览器开发者工具中复制)")
    
    cookie_string = input("Cookie字符串: ").strip()
    
    if not cookie_string:
        print("未输入Cookie字符串")
        return False
    
    try:
        # 解析cookie字符串
        cookies = {}
        for item in cookie_string.split(';'):
            if '=' in item:
                key, value = item.strip().split('=', 1)
                cookies[key] = value
        
        # 更新配置
        client = SimpleTemu()
        
        # 更新相关的cookies
        important_cookies = ['seller_temp', 'api_uid', '_bee', 'njrpl', 'mallid']
        updated_count = 0
        
        for cookie_name in important_cookies:
            if cookie_name in cookies:
                client.config['cookies'][cookie_name] = cookies[cookie_name]
                updated_count += 1
                print(f"✅ 更新 {cookie_name}")
        
        if updated_count > 0:
            client.save_config()
            print(f"\n成功更新 {updated_count} 个cookies")
            return True
        else:
            print("未找到需要更新的cookies")
            return False
            
    except Exception as e:
        print(f"更新cookies失败: {e}")
        return False


def main():
    """主函数"""
    while True:
        print("\n" + "="*50)
        print("Token 更新工具")
        print("="*50)
        print("1. 从cURL命令提取token")
        print("2. 手动输入seller_temp")
        print("3. 批量更新所有cookies")
        print("4. 检查当前token状态")
        print("5. 测试API请求")
        print("0. 退出")
        
        choice = input("\n请选择操作 (0-5): ").strip()
        
        if choice == '0':
            print("退出程序")
            break
        elif choice == '1':
            seller_temp = extract_token_from_curl()
            if seller_temp:
                client = SimpleTemu()
                if client.update_token(seller_temp):
                    print("✅ Token更新成功!")
                else:
                    print("❌ Token更新失败!")
        
        elif choice == '2':
            seller_temp = manual_input_token()
            if seller_temp:
                client = SimpleTemu()
                if client.update_token(seller_temp):
                    print("✅ Token更新成功!")
                else:
                    print("❌ Token更新失败!")
        
        elif choice == '3':
            if update_all_cookies():
                print("✅ Cookies更新成功!")
            else:
                print("❌ Cookies更新失败!")
        
        elif choice == '4':
            client = SimpleTemu()
            client.check_token_status()
        
        elif choice == '5':
            print("\n测试API请求...")
            client = SimpleTemu()
            orders = client.get_recent_orders()
            if orders:
                print("✅ API请求成功!")
                print(f"获取到 {len(orders.get('result', {}).get('orderList', []))} 条订单")
            else:
                print("❌ API请求失败!")
        
        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    main()
