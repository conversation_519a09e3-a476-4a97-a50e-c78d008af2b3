#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import json
import logging
from datetime import datetime, timedelta
from simple_temu_client import SimpleTemu


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('token_monitor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class TokenMonitor:
    def __init__(self, check_interval=3600):  # 默认每小时检查一次
        self.check_interval = check_interval  # 检查间隔（秒）
        self.client = SimpleTemu()
        self.last_check_time = None
        self.consecutive_failures = 0
        self.max_failures = 5
        
    def check_token_validity(self):
        """检查token有效性"""
        try:
            seller_temp = self.client.config['cookies'].get('seller_temp', '')
            is_valid, message = self.client.decode_seller_temp(seller_temp)
            
            logger.info(f"Token检查结果: {message}")
            
            if not is_valid:
                logger.warning("Token无效或已过期!")
                self.send_notification("Token过期", message)
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"检查token时出错: {e}")
            return False
    
    def test_api_request(self):
        """测试API请求"""
        try:
            logger.info("测试API请求...")
            
            # 尝试获取订单数据（只获取第一页，减少请求量）
            orders = self.client.get_recent_orders(page_number=1, page_size=10)
            
            if orders and orders.get('success'):
                order_count = len(orders.get('result', {}).get('orderList', []))
                logger.info(f"API请求成功，获取到 {order_count} 条订单")
                self.consecutive_failures = 0
                return True
            else:
                logger.warning("API请求失败或返回异常数据")
                self.consecutive_failures += 1
                return False
                
        except Exception as e:
            logger.error(f"API请求测试失败: {e}")
            self.consecutive_failures += 1
            return False
    
    def send_notification(self, title, message):
        """发送通知（可以扩展为邮件、微信等）"""
        notification = {
            "timestamp": datetime.now().isoformat(),
            "title": title,
            "message": message,
            "consecutive_failures": self.consecutive_failures
        }
        
        # 保存通知到文件
        try:
            with open('notifications.json', 'a', encoding='utf-8') as f:
                f.write(json.dumps(notification, ensure_ascii=False) + '\n')
        except Exception as e:
            logger.error(f"保存通知失败: {e}")
        
        # 这里可以添加其他通知方式
        # 例如：发送邮件、微信消息、钉钉消息等
        logger.warning(f"通知: {title} - {message}")
    
    def get_token_expiry_info(self):
        """获取token过期信息"""
        try:
            seller_temp = self.client.config['cookies'].get('seller_temp', '')
            is_valid, message = self.client.decode_seller_temp(seller_temp)
            
            if is_valid and "剩余" in message:
                # 提取剩余小时数
                import re
                hours_match = re.search(r'剩余 ([\d.]+) 小时', message)
                if hours_match:
                    remaining_hours = float(hours_match.group(1))
                    return remaining_hours
            
            return 0
            
        except Exception as e:
            logger.error(f"获取token过期信息失败: {e}")
            return 0
    
    def should_alert(self):
        """判断是否应该发送警告"""
        remaining_hours = self.get_token_expiry_info()
        
        # 如果剩余时间少于24小时，发送警告
        if 0 < remaining_hours < 24:
            return True, f"Token将在 {remaining_hours:.1f} 小时后过期"
        
        # 如果连续失败次数过多，发送警告
        if self.consecutive_failures >= self.max_failures:
            return True, f"连续 {self.consecutive_failures} 次API请求失败"
        
        return False, ""
    
    def run_check(self):
        """执行一次完整检查"""
        logger.info("开始执行token检查...")
        
        # 1. 检查token格式和过期时间
        token_valid = self.check_token_validity()
        
        # 2. 测试API请求
        api_valid = self.test_api_request()
        
        # 3. 判断是否需要发送警告
        should_alert, alert_message = self.should_alert()
        if should_alert:
            self.send_notification("Token警告", alert_message)
        
        # 4. 记录检查结果
        self.last_check_time = datetime.now()
        
        status = {
            "check_time": self.last_check_time.isoformat(),
            "token_valid": token_valid,
            "api_valid": api_valid,
            "consecutive_failures": self.consecutive_failures,
            "remaining_hours": self.get_token_expiry_info()
        }
        
        # 保存状态到文件
        try:
            with open('monitor_status.json', 'w', encoding='utf-8') as f:
                json.dump(status, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"保存状态失败: {e}")
        
        logger.info(f"检查完成 - Token有效: {token_valid}, API有效: {api_valid}")
        
        return token_valid and api_valid
    
    def run_monitor(self):
        """运行监控循环"""
        logger.info(f"开始Token监控，检查间隔: {self.check_interval} 秒")
        
        try:
            while True:
                self.run_check()
                
                logger.info(f"等待 {self.check_interval} 秒后进行下次检查...")
                time.sleep(self.check_interval)
                
        except KeyboardInterrupt:
            logger.info("监控被用户中断")
        except Exception as e:
            logger.error(f"监控过程中出错: {e}")
            self.send_notification("监控错误", str(e))


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Temu Token 监控工具')
    parser.add_argument('--interval', type=int, default=3600, 
                       help='检查间隔（秒），默认3600秒（1小时）')
    parser.add_argument('--once', action='store_true', 
                       help='只执行一次检查，不进入监控循环')
    
    args = parser.parse_args()
    
    monitor = TokenMonitor(check_interval=args.interval)
    
    if args.once:
        logger.info("执行单次检查...")
        success = monitor.run_check()
        if success:
            logger.info("检查完成，一切正常")
        else:
            logger.warning("检查发现问题，请查看日志")
    else:
        logger.info("启动持续监控...")
        monitor.run_monitor()


if __name__ == "__main__":
    main()
