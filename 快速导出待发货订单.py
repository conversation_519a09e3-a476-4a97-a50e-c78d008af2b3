#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Temu店铺待发货订单快速导出工具
简化版本，一键导出待发货订单
"""

from 待发货订单导出工具 import TemuOrderExporter
from datetime import datetime


def main():
    """快速导出主函数"""
    print("=" * 50)
    print("🚀 Temu店铺待发货订单快速导出工具")
    print("=" * 50)
    
    # 634418217262650, 634418217938579, 634418217955942, 634418217956223, 634418218259668, 634418218259845, 634418218260013, 634418218777560, 634418219045256, 634418220183483, 634418220183786
    # 使用现有的配置信息
    MALL_ID = "634418218777560"
    ACCESS_TOKEN = "J73OXMYWK2RLZ5MUWHZGAOF4J6VLXJ6PMAPBNX6VCIJBZBQBT7MA011025f145cf"
    
    COOKIES = {
        "api_uid": "CmwYKWgQb2yulABcDpOgAg==",
        "_bee": "jmWVoOOs6u8MTHaJej00jbAskNLUYanv",
        "njrpl": "jmWVoOOs6u8MTHaJej00jbAskNLUYanv",
        "dilx": "_EsYzO7-cUf1dj6XUNIzM",
        "hfsc": "L3yOfYAw7jv/0pXLfA==",
        "_nano_fp": "XpmYn5m8n5mbXpEJl9_7bNdmhzyXs6pb4Rmyvp~T",
        "AccessToken": ACCESS_TOKEN,
        "user_uin": "BBUSAO4SDG2TDHE743EOIUKYYGS4QSYPO2PKQSWC",
        "isLogin": "1747965800295",
        "region": "0",
        "timezone": "Asia%2FShanghai",
        "webp": "1",
        "seller_temp": "N_eyJ0Ijoiczg0NXFFUHFzYjRsZVNTZGlCYmlKV3FGZktlUkZDenNYZ20xMUxFaE9aRDBRQ3IrN04vc2lCdHVHZjBVc3B6cERCVXdaUzdFSHM0aENrNm5sMERDeFE9PSIsInYiOjEsInMiOjEwMDAxLCJ1IjoyMzkzNDI2MTU1MDQ3OH0=",
        "mallid": MALL_ID
    }
    
    print(f"📍 店铺ID: {MALL_ID}")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # 创建导出器
        exporter = TemuOrderExporter(MALL_ID, ACCESS_TOKEN, COOKIES)
        
        # 执行导出
        print("🔄 正在导出待发货订单...")
        result = exporter.export_orders(
            export_excel=True, 
            export_csv=True, 
            max_pages=20
        )
        
        # 显示结果
        print()
        if result['success']:
            print("✅ 导出成功!")
            print(f"📊 共导出 {result['order_count']} 个待发货订单")
            print()
            print("📁 生成的文件:")
            for file in result['files']:
                print(f"   📄 {file}")
            
            print()
            print("💡 提示:")
            print("   - Excel文件适合查看和打印")
            print("   - CSV文件适合导入其他系统")
            print("   - 紧急订单请优先处理")
            
        else:
            print(f"❌ 导出失败: {result['message']}")
            
    except Exception as e:
        print(f"❌ 发生错误: {str(e)}")
        print("💡 请检查网络连接和登录状态")
    
    print()
    print("=" * 50)
    print("🏁 导出完成")
    print("=" * 50)


if __name__ == "__main__":
    main()
