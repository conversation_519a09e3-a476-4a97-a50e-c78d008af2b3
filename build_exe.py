#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多店铺快速导出工具 - EXE打包脚本
"""

import os
import subprocess
import sys
from pathlib import Path

def build_exe():
    """构建EXE文件"""
    
    print("🚀 开始打包多店铺快速导出工具...")
    print("=" * 60)
    
    # 检查源文件
    source_file = "demo.py"
    if not os.path.exists(source_file):
        print(f"❌ 源文件不存在: {source_file}")
        return False
    
    # 检查依赖文件
    dependency_file = "待发货订单导出工具.py"
    if not os.path.exists(dependency_file):
        print(f"❌ 依赖文件不存在: {dependency_file}")
        return False
    
    print(f"✅ 源文件检查通过: {source_file}")
    print(f"✅ 依赖文件检查通过: {dependency_file}")
    
    # PyInstaller命令参数
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个exe文件
        "--console",                    # 显示控制台窗口（交互式应用需要）
        "--name=多店铺Temu订单导出工具",   # 指定exe文件名
        "--clean",                      # 清理临时文件
        "--noconfirm",                  # 不询问确认
        f"--add-data={dependency_file};.",  # 添加依赖文件
        source_file
    ]
    
    print(f"\n🔧 执行打包命令:")
    print(" ".join(cmd))
    print()
    
    try:
        # 执行打包
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 打包成功!")
            
            # 检查生成的exe文件
            exe_path = Path("dist") / "多店铺Temu订单导出工具.exe"
            if exe_path.exists():
                file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
                print(f"📁 生成的EXE文件: {exe_path}")
                print(f"📊 文件大小: {file_size:.1f} MB")
                
                # 提供使用说明
                print("\n" + "=" * 60)
                print("🎉 打包完成!")
                print("=" * 60)
                print(f"📁 EXE文件位置: {exe_path.absolute()}")
                print("\n📋 使用说明:")
                print("1. 双击运行 '多店铺Temu订单导出工具.exe'")
                print("2. 按照提示选择导出模式")
                print("3. 导出的文件会保存在exe文件同目录下")
                print("\n⚠️  注意事项:")
                print("- 首次运行可能需要几秒钟启动时间")
                print("- 确保网络连接正常")
                print("- 如果AccessToken过期，需要更新源代码重新打包")
                
                return True
            else:
                print("❌ 未找到生成的EXE文件")
                return False
        else:
            print("❌ 打包失败!")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 打包过程中发生异常: {e}")
        return False

def clean_build_files():
    """清理构建文件"""
    import shutil
    
    dirs_to_clean = ["build", "__pycache__"]
    files_to_clean = ["*.spec"]
    
    print("\n🧹 清理构建文件...")
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"   删除目录: {dir_name}")
    
    import glob
    for pattern in files_to_clean:
        for file in glob.glob(pattern):
            os.remove(file)
            print(f"   删除文件: {file}")

if __name__ == "__main__":
    print("多店铺Temu订单导出工具 - EXE打包器")
    print("=" * 60)
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        sys.exit(1)
    
    print(f"✅ Python版本: {sys.version}")
    
    # 执行打包
    success = build_exe()
    
    if success:
        # 询问是否清理构建文件
        try:
            choice = input("\n是否清理构建文件？(y/n): ").strip().lower()
            if choice == 'y':
                clean_build_files()
                print("✅ 构建文件清理完成")
        except KeyboardInterrupt:
            print("\n用户取消操作")
    
    print("\n🏁 打包流程结束")
