import requests
import json
import time
import base64
import os
from datetime import datetime


class SimpleTemu:
    def __init__(self, config_file='config.json'):
        self.config_file = config_file
        self.config = self.load_config()
        
    def load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # 如果没有配置文件，使用默认配置
            return self.get_default_config()
    
    def get_default_config(self):
        """获取默认配置"""
        return {
            "cookies": {
                "api_uid": "Ch/2Ymho0bubXwBEYfRcAg==",
                "region": "0",
                "timezone": "Asia%2FShanghai",
                "webp": "1",
                "_nano_fp": "XpmyXpEblpUoX0P8nT_h4E20KLbOz36pvEa0xe86",
                "_bee": "dbyVpvYXjDgt13x7kJTaAmCsARZDbapU",
                "njrpl": "dbyVpvYXjDgt13x7kJTaAmCsARZDbapU",
                "dilx": "1-NMjn5l45qOxniKxontR",
                "hfsc": "L3yPeY854Df415HEeg==",
                "seller_temp": "N_eyJ0IjoiemFrMklmODBwUi9Xb2lQSWZVQ2llUFJaVDdlRlNwVUFkYk9ETU12L0VBQ1dxQ2JtSHFIZnhQOEZocEhYNlYyb3ZocmdHT21NRnRTSnpaWnV2Y1NUd1E9PSIsInYiOjEsInMiOjEwMDAxLCJ1IjoyNDAwMTcxMDM1MzQ0Nn0=",
                "mallid": "634418218659715"
            },
            "headers": {
                "accept": "application/json, text/plain, */*",
                "accept-language": "zh-CN,zh;q=0.9",
                "content-type": "application/json;charset=UTF-8",
                "mallid": "634418218659715",
                "origin": "https://agentseller-us.temu.com",
                "priority": "u=1, i",
                "referer": "https://agentseller-us.temu.com/mmsos/orders.html",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"
            }
        }
    
    def save_config(self):
        """保存配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=4, ensure_ascii=False)
    
    def decode_seller_temp(self, seller_temp):
        """解码seller_temp参数，检查过期时间"""
        try:
            if not seller_temp or not seller_temp.startswith('N_'):
                return False, "无效的token格式"
                
            encoded_data = seller_temp[2:]
            decoded_bytes = base64.b64decode(encoded_data)
            decoded_str = decoded_bytes.decode('utf-8')
            token_data = json.loads(decoded_str)
            
            if 'u' in token_data:
                expire_time = token_data['u'] / 1000
                current_time = time.time()
                
                if current_time > expire_time:
                    return False, f"Token已过期: {datetime.fromtimestamp(expire_time)}"
                else:
                    remaining_hours = (expire_time - current_time) / 3600
                    return True, f"Token有效期至: {datetime.fromtimestamp(expire_time)} (剩余 {remaining_hours:.1f} 小时)"
            else:
                return False, "Token中没有过期时间信息"
                
        except Exception as e:
            return False, f"解析token失败: {e}"
    
    def update_token(self, new_seller_temp):
        """手动更新token"""
        is_valid, message = self.decode_seller_temp(new_seller_temp)
        
        if is_valid:
            self.config['cookies']['seller_temp'] = new_seller_temp
            self.save_config()
            print(f"Token更新成功: {message}")
            return True
        else:
            print(f"Token无效: {message}")
            return False
    
    def check_token_status(self):
        """检查当前token状态"""
        seller_temp = self.config['cookies'].get('seller_temp', '')
        is_valid, message = self.decode_seller_temp(seller_temp)
        print(f"Token状态: {message}")
        return is_valid
    
    def make_request(self, url, data=None, max_retries=3):
        """发送请求"""
        for attempt in range(max_retries):
            try:
                response = requests.post(
                    url,
                    headers=self.config['headers'],
                    cookies=self.config['cookies'],
                    data=json.dumps(data, separators=(',', ':')) if data else None,
                    timeout=30
                )
                
                # 检查响应状态
                if response.status_code == 200:
                    return response
                elif response.status_code == 403:
                    try:
                        error_data = response.json()
                        if error_data.get('error_code') == 40001:
                            print(f"第{attempt + 1}次尝试失败: 登录状态失效 (错误码: 40001)")
                            print("请手动更新seller_temp token")
                            print("使用方法: client.update_token('新的seller_temp值')")
                            
                            if attempt < max_retries - 1:
                                print("等待5秒后重试...")
                                time.sleep(5)
                                continue
                    except:
                        pass
                
                print(f"请求失败，状态码: {response.status_code}")
                return response
                
            except requests.exceptions.RequestException as e:
                print(f"请求异常 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)
                    continue
                else:
                    raise
        
        return None
    
    def get_recent_orders(self, page_number=1, page_size=500):
        """获取最近订单列表"""
        url = "https://agentseller-us.temu.com/kirogi/bg/mms/recentOrderList"
        data = {
            "fulfillmentMode": 0,
            "pageNumber": page_number,
            "pageSize": page_size,
            "queryType": 0,
            "sortType": 1,
            "timeZone": "UTC+8",
            "parentAfterSalesTag": 0,
            "sellerNoteLabelList": []
        }
        
        print("正在获取订单数据...")
        response = self.make_request(url, data)
        
        if response and response.status_code == 200:
            try:
                return response.json()
            except json.JSONDecodeError:
                print("响应不是有效的JSON格式")
                return None
        else:
            print(f"获取订单失败")
            if response:
                print(f"响应内容: {response.text}")
            return None


def main():
    """主函数"""
    print("=== Temu API 客户端 ===")
    
    # 创建客户端实例
    client = SimpleTemu()
    
    # 检查token状态
    print("\n1. 检查Token状态:")
    is_valid = client.check_token_status()
    
    if not is_valid:
        print("\n⚠️  Token已过期或无效!")
        print("请按以下步骤更新token:")
        print("1. 打开浏览器，登录到 https://agentseller-us.temu.com")
        print("2. 打开开发者工具 (F12)")
        print("3. 在Network标签页中找到任意API请求")
        print("4. 复制Cookie中的seller_temp值")
        print("5. 运行: client.update_token('新的seller_temp值')")
        print("\n或者直接修改config.json文件中的seller_temp值")
        
        # 询问是否继续尝试
        user_input = input("\n是否仍要尝试请求? (y/n): ")
        if user_input.lower() != 'y':
            return
    
    # 尝试获取订单数据
    print("\n2. 获取订单数据:")
    orders = client.get_recent_orders()
    
    if orders:
        print("✅ 成功获取订单数据!")
        print(f"订单数量: {len(orders.get('result', {}).get('orderList', []))}")
        
        # 保存到文件
        with open('orders.json', 'w', encoding='utf-8') as f:
            json.dump(orders, f, indent=2, ensure_ascii=False)
        print("订单数据已保存到 orders.json")
        
    else:
        print("❌ 获取订单数据失败")


if __name__ == "__main__":
    main()
