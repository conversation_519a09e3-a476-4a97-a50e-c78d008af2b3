#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多店铺Temu待发货订单快速导出工具
无需浏览器，直接使用店铺ID列表进行批量导出
"""

from 待发货订单导出工具 import TemuOrderExporter
from datetime import datetime
import pandas as pd
import os
from concurrent.futures import ThreadPoolExecutor, as_completed


def export_single_store(mall_id, access_token, cookies, store_name=None):
    """导出单个店铺的待发货订单"""
    if not store_name:
        store_name = f"店铺_{mall_id}"

    print(f"\n🏪 开始处理店铺: {store_name} (ID: {mall_id})")

    try:
        # 创建导出器
        exporter = TemuOrderExporter(mall_id, access_token, cookies)

        # 执行导出
        result = exporter.export_orders(
            export_excel=True,
            export_csv=True,
            max_pages=20
        )

        if result['success']:
            print(f"   ✅ 店铺 {store_name} 完成，导出 {result['order_count']} 个待发货订单")
            return {
                'mall_id': mall_id,
                'store_name': store_name,
                'success': True,
                'order_count': result['order_count'],
                'files': result['files'],
                'orders': result.get('orders', [])  # 添加订单数据用于合并
            }
        else:
            print(f"   ❌ 店铺 {store_name} 导出失败: {result['message']}")
            return {
                'mall_id': mall_id,
                'store_name': store_name,
                'success': False,
                'error': result['message'],
                'order_count': 0,
                'files': [],
                'orders': []
            }

    except Exception as e:
        print(f"   ❌ 店铺 {store_name} 发生异常: {str(e)}")
        return {
            'mall_id': mall_id,
            'store_name': store_name,
            'success': False,
            'error': str(e),
            'order_count': 0,
            'files': [],
            'orders': []
        }


def export_multiple_stores(store_configs, max_workers=3):
    """并发导出多个店铺的待发货订单"""
    print(f"\n🏭 开始并发处理 {len(store_configs)} 个店铺...")
    
    results = []
    
    # 使用线程池并发处理
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_store = {}
        
        for config in store_configs:
            future = executor.submit(
                export_single_store,
                config['mall_id'],
                config['access_token'],
                config['cookies'],
                config.get('store_name')
            )
            future_to_store[future] = config
        
        for future in as_completed(future_to_store):
            config = future_to_store[future]
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                print(f"❌ 处理店铺 {config.get('store_name', config['mall_id'])} 时发生异常: {e}")
                results.append({
                    'mall_id': config['mall_id'],
                    'store_name': config.get('store_name', f"店铺_{config['mall_id']}"),
                    'success': False,
                    'error': str(e),
                    'order_count': 0,
                    'files': []
                })
    
    return results


def generate_consolidated_report(results):
    """生成合并的总表格"""
    from datetime import datetime
    import pandas as pd

    all_orders = []

    # 收集所有店铺的订单数据
    for result in results:
        if result['success'] and result.get('orders'):
            for order in result['orders']:
                # 为每个订单添加店铺信息
                order_with_store = order.copy()
                order_with_store['店铺ID'] = result['mall_id']
                order_with_store['店铺名称'] = result['store_name']
                all_orders.append(order_with_store)

    if not all_orders:
        print("❌ 没有订单数据可以合并")
        return None

    # 创建DataFrame
    df = pd.DataFrame(all_orders)

    # 生成文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    excel_filename = f"多店铺待发货订单汇总_{timestamp}.xlsx"
    csv_filename = f"多店铺待发货订单汇总_{timestamp}.csv"

    try:
        # 导出Excel
        with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='所有店铺汇总', index=False)

            # 调整列宽
            worksheet = writer.sheets['所有店铺汇总']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        # 导出CSV
        df.to_csv(csv_filename, index=False, encoding='utf-8-sig')

        print(f"\n📊 合并表格导出成功:")
        print(f"   📄 {excel_filename} (包含 {len(all_orders)} 个订单)")
        print(f"   📄 {csv_filename} (包含 {len(all_orders)} 个订单)")

        return {
            'excel_file': excel_filename,
            'csv_file': csv_filename,
            'total_orders': len(all_orders)
        }

    except Exception as e:
        print(f"❌ 合并表格导出失败: {e}")
        return None


def generate_summary_report(results, generate_consolidated=True):
    """生成汇总报告"""
    print("\n" + "="*60)
    print("🎉 多店铺待发货订单导出完成!")
    print("="*60)

    # 统计信息
    total_stores = len(results)
    successful_stores = len([r for r in results if r['success']])
    total_orders = sum(r['order_count'] for r in results if r['success'])

    print(f"📊 导出统计:")
    print(f"   处理店铺数: {total_stores}")
    print(f"   成功店铺数: {successful_stores}")
    print(f"   失败店铺数: {total_stores - successful_stores}")
    print(f"   待发货订单总数: {total_orders}")

    # 按店铺显示详情
    print(f"\n📋 各店铺详情:")
    for result in results:
        status = "✅" if result['success'] else "❌"
        if result['success']:
            print(f"   {status} {result['store_name']}: {result['order_count']} 个订单")
            for file in result['files']:
                print(f"      📄 {file}")
        else:
            print(f"   {status} {result['store_name']}: 失败 - {result.get('error', '未知错误')}")

    # 生成合并表格
    if generate_consolidated and successful_stores > 0:
        print(f"\n🔄 正在生成合并表格...")
        consolidated_result = generate_consolidated_report(results)
        if consolidated_result:
            print(f"✅ 合并表格生成完成!")
        else:
            print(f"❌ 合并表格生成失败")


def main():
    """主函数"""
    print("=" * 70)
    print("🚀 多店铺Temu待发货订单快速导出工具")
    print("=" * 70)
    
    # 所有店铺ID列表
    ALL_MALL_IDS = [
        "634418217262650", "634418217938579", "634418217955942", 
        "634418217956223", "634418218259668", "634418218259845", 
        "634418218260013", "634418218777560", "634418219045256", 
        "634418220183483", "634418220183786"
    ]
    
    # 通用配置
    ACCESS_TOKEN = "J73OXMYWK2RLZ5MUWHZGAOF4J6VLXJ6PMAPBNX6VCIJBZBQBT7MA011025f145cf"
    
    BASE_COOKIES = {
        "api_uid": "CmwYKWgQb2yulABcDpOgAg==",
        "_bee": "jmWVoOOs6u8MTHaJej00jbAskNLUYanv",
        "njrpl": "jmWVoOOs6u8MTHaJej00jbAskNLUYanv",
        "dilx": "_EsYzO7-cUf1dj6XUNIzM",
        "hfsc": "L3yOfYAw7jv/0pXLfA==",
        "_nano_fp": "XpmYn5m8n5mbXpEJl9_7bNdmhzyXs6pb4Rmyvp~T",
        "AccessToken": ACCESS_TOKEN,
        "user_uin": "BBUSAO4SDG2TDHE743EOIUKYYGS4QSYPO2PKQSWC",
        "isLogin": "1747965800295",
        "region": "0",
        "timezone": "Asia%2FShanghai",
        "webp": "1",
        "seller_temp": "N_eyJ0IjoiWkkwZlhCUENHbm5SdU5PaGYzaWppaW5ESnBFNHN2b1VkNHZpSHVIZmxWNFczU0ZlNG1zUFZMSTYwamJHMVBHM0R6WEM4ZDJkQjh0N1YwRHgrc3RBbUE9PSIsInYiOjEsInMiOjEwMDAxLCJ1IjoyMzkzNDI2MTU1MDQ3OH0="
    }
    
    print(f"📍 发现 {len(ALL_MALL_IDS)} 个店铺ID")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 选择导出模式
    print("\n🔧 请选择导出模式:")
    print("1. 单店铺导出 (当前店铺: 634418218777560)")
    print("2. 多店铺导出 (所有11个店铺)")
    print("3. 自定义店铺列表导出")

    choice = input("\n请输入选择 (1/2/3): ").strip()

    # 对于多店铺导出，询问是否生成合并表格
    generate_consolidated = False
    if choice in ["2", "3"]:
        consolidate_choice = input("\n是否生成合并的总表格？(y/n): ").strip().lower()
        generate_consolidated = (consolidate_choice == 'y')
    
    if choice == "1":
        # 单店铺导出
        current_mall_id = "634418218777560"
        cookies = BASE_COOKIES.copy()
        cookies["mallid"] = str(current_mall_id)  # 确保mallid是字符串类型
        
        print(f"\n🔄 正在导出单店铺: {current_mall_id}")
        
        result = export_single_store(
            current_mall_id, 
            ACCESS_TOKEN, 
            cookies, 
            f"当前店铺_{current_mall_id}"
        )
        
        if result['success']:
            print(f"\n✅ 导出成功!")
            print(f"📊 共导出 {result['order_count']} 个待发货订单")
            print(f"\n📁 生成的文件:")
            for file in result['files']:
                print(f"   📄 {file}")
        else:
            print(f"\n❌ 导出失败: {result.get('error', '未知错误')}")
    
    elif choice == "2":
        # 多店铺导出
        print(f"\n🔄 正在准备导出所有 {len(ALL_MALL_IDS)} 个店铺...")
        
        # 准备店铺配置
        store_configs = []
        for mall_id in ALL_MALL_IDS:
            cookies = BASE_COOKIES.copy()
            cookies["mallid"] = str(mall_id)  # 确保mallid是字符串类型

            store_configs.append({
                'mall_id': mall_id,
                'store_name': f"店铺_{mall_id}",
                'access_token': ACCESS_TOKEN,
                'cookies': cookies
            })
        
        # 确认开始
        confirm = input(f"\n是否开始导出所有 {len(store_configs)} 个店铺的待发货订单？(y/n): ").strip().lower()
        if confirm != 'y':
            print("❌ 用户取消操作")
            return
        
        # 执行多店铺导出
        results = export_multiple_stores(store_configs, max_workers=3)

        # 生成汇总报告
        generate_summary_report(results, generate_consolidated)
    
    elif choice == "3":
        # 自定义店铺列表
        print(f"\n📋 所有可用店铺ID:")
        for i, mall_id in enumerate(ALL_MALL_IDS, 1):
            print(f"   {i}. {mall_id}")
        
        print(f"\n请输入要导出的店铺编号 (用逗号分隔，如: 1,3,5):")
        selected_input = input("编号: ").strip()
        
        try:
            selected_indices = [int(x.strip()) - 1 for x in selected_input.split(',')]
            selected_mall_ids = [ALL_MALL_IDS[i] for i in selected_indices if 0 <= i < len(ALL_MALL_IDS)]
            
            if not selected_mall_ids:
                print("❌ 没有选择有效的店铺")
                return
            
            print(f"\n✅ 已选择 {len(selected_mall_ids)} 个店铺:")
            for mall_id in selected_mall_ids:
                print(f"   📍 {mall_id}")
            
            # 准备店铺配置
            store_configs = []
            for mall_id in selected_mall_ids:
                cookies = BASE_COOKIES.copy()
                cookies["mallid"] = str(mall_id)  # 确保mallid是字符串类型

                store_configs.append({
                    'mall_id': mall_id,
                    'store_name': f"店铺_{mall_id}",
                    'access_token': ACCESS_TOKEN,
                    'cookies': cookies
                })
            
            # 确认开始
            confirm = input(f"\n是否开始导出选中的 {len(store_configs)} 个店铺的待发货订单？(y/n): ").strip().lower()
            if confirm != 'y':
                print("❌ 用户取消操作")
                return
            
            # 执行导出
            results = export_multiple_stores(store_configs, max_workers=3)

            # 生成汇总报告
            generate_summary_report(results, generate_consolidated)
            
        except Exception as e:
            print(f"❌ 输入格式错误: {e}")
            return
    
    else:
        print("❌ 无效选择")
        return
    
    print("\n" + "=" * 70)
    print("🏁 导出完成")
    print("=" * 70)


if __name__ == "__main__":
    main()
