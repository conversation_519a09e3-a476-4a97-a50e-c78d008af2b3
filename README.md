# Temu API 客户端 - 解决登录状态失效问题

## 问题描述
原始脚本遇到的 `{"error_msg":"Invalid Login State","error_code":40001}` 错误是因为 `seller_temp` 参数过期导致的。

## 解决方案

### 方案1: 使用简化版客户端 (推荐)

使用 `simple_temu_client.py`，这是最简单的解决方案：

```python
from simple_temu_client import SimpleTemu

# 创建客户端
client = SimpleTemu()

# 检查token状态
client.check_token_status()

# 获取订单数据
orders = client.get_recent_orders()
```

### 方案2: 手动更新Token

当token过期时，按以下步骤手动更新：

1. **获取新的seller_temp值:**
   - 打开浏览器，登录到 https://agentseller-us.temu.com
   - 打开开发者工具 (F12)
   - 切换到 Network 标签页
   - 刷新页面或执行任何操作
   - 找到任意API请求，查看Request Headers中的Cookie
   - 复制 `seller_temp` 的值

2. **更新token:**
   ```python
   client = SimpleTemu()
   client.update_token('新的seller_temp值')
   ```

### 方案3: 使用完整版客户端 (需要selenium)

使用 `temu_api_client.py`，支持自动登录：

```python
from temu_api_client import TemuAPIClient

# 首先在config.json中设置用户名和密码
client = TemuAPIClient()
orders = client.get_recent_orders()
```

## 文件说明

### 核心文件
- `simple_temu_client.py` - **主要客户端**，简单易用，推荐使用
- `update_token.py` - **Token更新工具**，交互式更新token
- `token_monitor.py` - **监控工具**，自动检查token状态
- `config.json` - 配置文件，存储cookies和headers

### 其他文件
- `demo.py` - 原始脚本的改进版本，增加了重试机制和token检查
- `temu_api_client.py` - 完整版客户端，支持自动登录 (需要安装selenium)
- `README.md` - 使用说明

### 生成的文件
- `orders.json` - 订单数据
- `token_monitor.log` - 监控日志
- `monitor_status.json` - 监控状态
- `notifications.json` - 通知记录

## 安装依赖

基础版本：
```bash
pip install requests
```

完整版本（如果使用自动登录功能）：
```bash
pip install requests selenium
```

## 使用步骤

### 快速开始

1. **运行简化版客户端:**
   ```bash
   python simple_temu_client.py
   ```

2. **如果token过期，使用更新工具:**
   ```bash
   python update_token.py
   ```

3. **程序会自动保存订单数据到 `orders.json`**

### 详细步骤

#### 1. 首次使用
```bash
# 检查token状态
python simple_temu_client.py
```

#### 2. 更新Token（当出现40001错误时）
```bash
# 使用交互式更新工具
python update_token.py

# 或者直接在代码中更新
python -c "
from simple_temu_client import SimpleTemu
client = SimpleTemu()
client.update_token('你的新token')
"
```

#### 3. 设置监控（可选）
```bash
# 单次检查
python token_monitor.py --once

# 持续监控（每小时检查一次）
python token_monitor.py

# 自定义检查间隔（每30分钟检查一次）
python token_monitor.py --interval 1800
```

## Token管理技巧

### 1. 定期检查token状态
```python
client = SimpleTemu()
is_valid = client.check_token_status()
```

### 2. 批量更新多个参数
直接编辑 `config.json` 文件，更新所有必要的cookies和headers。

### 3. 设置定时任务
可以设置定时任务定期检查和更新token：

```python
import schedule
import time

def check_and_update():
    client = SimpleTemu()
    if not client.check_token_status():
        print("Token需要更新!")
        # 这里可以添加通知逻辑

# 每小时检查一次
schedule.every().hour.do(check_and_update)

while True:
    schedule.run_pending()
    time.sleep(60)
```

## 常见问题

### Q: 为什么token会过期？
A: `seller_temp` 是一个JWT token，包含过期时间。通常在几小时到几天后过期。

### Q: 如何延长token有效期？
A: 无法延长现有token，只能获取新的token。建议：
- 定期检查token状态
- 设置自动更新机制
- 保持浏览器会话活跃

### Q: 自动登录不工作怎么办？
A: 自动登录可能因为网站更新而失效。建议：
- 使用手动更新方式
- 检查登录页面元素是否变化
- 考虑使用验证码识别服务

## 安全建议

1. **不要在代码中硬编码敏感信息**
2. **定期更换密码**
3. **使用环境变量存储敏感配置**
4. **限制API访问频率，避免被封禁**

## 监控和日志

程序会输出详细的状态信息：
- Token有效期检查结果
- 请求成功/失败状态
- 错误详细信息

建议将这些信息记录到日志文件中，便于问题排查。
