import requests
import json
import time
import base64
from datetime import datetime, timedelta


def refresh_seller_temp():
    """
    刷新seller_temp参数的函数
    这里需要根据实际情况实现自动登录逻辑
    """
    # TODO: 实现自动登录逻辑，获取新的seller_temp
    # 可以通过模拟登录流程来获取新的认证token
    pass

def decode_seller_temp(seller_temp):
    """
    解码seller_temp参数，检查过期时间
    """
    try:
        # 去掉前缀 "N_"
        encoded_data = seller_temp[2:]
        # Base64解码
        decoded_bytes = base64.b64decode(encoded_data)
        decoded_str = decoded_bytes.decode('utf-8')
        token_data = json.loads(decoded_str)

        # 检查过期时间 (u字段通常是时间戳)
        if 'u' in token_data:
            expire_time = token_data['u'] / 1000  # 转换为秒
            current_time = time.time()
            if current_time > expire_time:
                print(f"Token已过期: {datetime.fromtimestamp(expire_time)}")
                return False
            else:
                print(f"Token有效期至: {datetime.fromtimestamp(expire_time)}")
                return True
    except Exception as e:
        print(f"解析token失败: {e}")
        return False

def make_request_with_retry():
    """
    带重试机制的请求函数
    """
    max_retries = 3
    for attempt in range(max_retries):
        try:
            response = requests.post(url, headers=headers, cookies=cookies, data=data)

            if response.status_code == 403:
                error_data = response.json()
                if error_data.get('error_code') == 40001:
                    print(f"第{attempt + 1}次尝试: 登录状态失效，尝试刷新token...")
                    # 这里可以调用refresh_seller_temp()来获取新的token
                    # refresh_seller_temp()
                    time.sleep(2)  # 等待2秒后重试
                    continue

            return response

        except Exception as e:
            print(f"请求失败: {e}")
            if attempt < max_retries - 1:
                time.sleep(2)
                continue
            else:
                raise

    return None

headers = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "zh-CN,zh;q=0.9",
    "content-type": "application/json;charset=UTF-8",
    "mallid": "634418218659715",
    "origin": "https://agentseller-us.temu.com",
    "priority": "u=1, i",
    "referer": "https://agentseller-us.temu.com/mmsos/orders.html?fulfillmentMode=0&queryT ype=0&sortType=1&timeZone=UTC%2B8&needBuySignService=0&sellerNoteLabelList=&packageAbnormalTypeList=",
    "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
    "x-document-referer": "https://agentseller-us.temu.com/main/authentication?redirectUrl=https%3A%2F%2Fagentseller-us.temu.com%2Fmmsos%2Forders.html%3Finit%3Dtrue%26mallId%3D634418220181826",
    "x-phan-data": "0aeJx7xMxiYPiI2Sra0NzU0NzAAIgtjIx04DwjY1PzWACtKgiF"
}
cookies = {
    "api_uid": "Ch/2Ymho0bubXwBEYfRcAg==",
    "region": "0",
    "timezone": "Asia%2FShanghai",
    "webp": "1",
    "_nano_fp": "XpmyXpEblpUoX0P8nT_h4E20KLbOz36pvEa0xe86",
    "_bee": "dbyVpvYXjDgt13x7kJTaAmCsARZDbapU",
    "njrpl": "dbyVpvYXjDgt13x7kJTaAmCsARZDbapU",
    "dilx": "1-NMjn5l45qOxniKxontR",
    "hfsc": "L3yPeY854Df415HEeg==",
    "seller_temp": "N_eyJ0IjoiemFrMklmODBwUi9Xb2lQSWZVQ2llUFJaVDdlRlNwVUFkYk9ETU12L0VBQ1dxQ2JtSHFIZnhQOEZocEhYNlYyb3ZocmdHT21NRnRTSnpaWnV2Y1NUd1E9PSIsInYiOjEsInMiOjEwMDAxLCJ1IjoyNDAwMTcxMDM1MzQ0Nn0=",
    "mallid": "634418218659715"
}
url = "https://agentseller-us.temu.com/kirogi/bg/mms/recentOrderList"
data = {
    "fulfillmentMode": 0,
    "pageNumber": 1,
    "pageSize": 500,
    "queryType": 0,
    "sortType": 1,
    "timeZone": "UTC+8",
    "parentAfterSalesTag": 0,
    "sellerNoteLabelList": []
}
data = json.dumps(data, separators=(',', ':'))

# 检查当前token是否有效
current_seller_temp = cookies.get('seller_temp', '')
if current_seller_temp:
    is_valid = decode_seller_temp(current_seller_temp)
    if not is_valid:
        print("警告: 当前token可能已过期，建议更新")

# 使用带重试机制的请求
response = make_request_with_retry()

if response:
    if response.status_code == 200:
        print("请求成功!")
        print(response.text)
    else:
        print(f"请求失败，状态码: {response.status_code}")
        print(response.text)
else:
    print("所有重试都失败了")