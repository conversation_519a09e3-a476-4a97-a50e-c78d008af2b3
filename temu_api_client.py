import requests
import json
import time
import base64
import os
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options


class TemuAPIClient:
    def __init__(self, config_file='config.json'):
        self.config_file = config_file
        self.config = self.load_config()
        self.session = requests.Session()
        
    def load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            raise FileNotFoundError(f"配置文件 {self.config_file} 不存在")
    
    def save_config(self):
        """保存配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=4, ensure_ascii=False)
    
    def decode_seller_temp(self, seller_temp):
        """解码seller_temp参数，检查过期时间"""
        try:
            if not seller_temp.startswith('N_'):
                return False
                
            encoded_data = seller_temp[2:]
            decoded_bytes = base64.b64decode(encoded_data)
            decoded_str = decoded_bytes.decode('utf-8')
            token_data = json.loads(decoded_str)
            
            if 'u' in token_data:
                expire_time = token_data['u'] / 1000
                current_time = time.time()
                
                if current_time > expire_time:
                    print(f"Token已过期: {datetime.fromtimestamp(expire_time)}")
                    return False
                else:
                    remaining_time = expire_time - current_time
                    print(f"Token有效期至: {datetime.fromtimestamp(expire_time)} (剩余 {remaining_time/3600:.1f} 小时)")
                    return True
        except Exception as e:
            print(f"解析token失败: {e}")
            return False
    
    def auto_login(self):
        """自动登录获取新的token"""
        print("开始自动登录...")
        
        # 检查是否有登录凭据
        credentials = self.config.get('login_credentials', {})
        if not credentials.get('username') or not credentials.get('password'):
            print("错误: 请在config.json中设置用户名和密码")
            return False
        
        try:
            # 设置Chrome选项
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # 无头模式
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            
            driver = webdriver.Chrome(options=chrome_options)
            
            # 访问登录页面
            login_url = credentials.get('login_url', 'https://agentseller-us.temu.com/main/authentication')
            driver.get(login_url)
            
            # 等待页面加载并输入凭据
            wait = WebDriverWait(driver, 10)
            
            # 这里需要根据实际的登录页面元素来调整
            username_field = wait.until(EC.presence_of_element_located((By.NAME, "username")))
            password_field = driver.find_element(By.NAME, "password")
            
            username_field.send_keys(credentials['username'])
            password_field.send_keys(credentials['password'])
            
            # 点击登录按钮
            login_button = driver.find_element(By.XPATH, "//button[@type='submit']")
            login_button.click()
            
            # 等待登录完成
            time.sleep(5)
            
            # 获取cookies
            cookies = driver.get_cookies()
            
            # 更新配置中的cookies
            for cookie in cookies:
                if cookie['name'] in self.config['cookies']:
                    self.config['cookies'][cookie['name']] = cookie['value']
            
            driver.quit()
            
            # 保存更新的配置
            self.save_config()
            print("自动登录成功，token已更新")
            return True
            
        except Exception as e:
            print(f"自动登录失败: {e}")
            return False
    
    def check_and_refresh_token(self):
        """检查并刷新token"""
        seller_temp = self.config['cookies'].get('seller_temp', '')
        
        if not seller_temp:
            print("未找到seller_temp，尝试自动登录...")
            return self.auto_login()
        
        if not self.decode_seller_temp(seller_temp):
            print("Token已过期，尝试自动登录...")
            return self.auto_login()
        
        return True
    
    def make_request(self, url, data=None, method='POST'):
        """发送请求，带自动重试和token刷新"""
        max_retries = self.config.get('settings', {}).get('max_retries', 3)
        retry_delay = self.config.get('settings', {}).get('retry_delay', 2)
        
        for attempt in range(max_retries):
            try:
                # 检查token有效性
                if not self.check_and_refresh_token():
                    print("无法获取有效token")
                    return None
                
                # 发送请求
                if method.upper() == 'POST':
                    response = self.session.post(
                        url, 
                        headers=self.config['headers'], 
                        cookies=self.config['cookies'], 
                        data=json.dumps(data, separators=(',', ':')) if data else None
                    )
                else:
                    response = self.session.get(
                        url, 
                        headers=self.config['headers'], 
                        cookies=self.config['cookies']
                    )
                
                # 检查响应
                if response.status_code == 403:
                    try:
                        error_data = response.json()
                        if error_data.get('error_code') == 40001:
                            print(f"第{attempt + 1}次尝试: 登录状态失效，尝试刷新token...")
                            if self.auto_login():
                                time.sleep(retry_delay)
                                continue
                    except:
                        pass
                
                return response
                
            except Exception as e:
                print(f"请求失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue
                else:
                    raise
        
        return None
    
    def get_recent_orders(self, page_number=1, page_size=500):
        """获取最近订单列表"""
        url = "https://agentseller-us.temu.com/kirogi/bg/mms/recentOrderList"
        data = {
            "fulfillmentMode": 0,
            "pageNumber": page_number,
            "pageSize": page_size,
            "queryType": 0,
            "sortType": 1,
            "timeZone": "UTC+8",
            "parentAfterSalesTag": 0,
            "sellerNoteLabelList": []
        }
        
        response = self.make_request(url, data)
        
        if response and response.status_code == 200:
            return response.json()
        else:
            print(f"获取订单失败: {response.status_code if response else 'No response'}")
            return None


# 使用示例
if __name__ == "__main__":
    try:
        client = TemuAPIClient()
        
        # 获取订单数据
        orders = client.get_recent_orders()
        
        if orders:
            print("成功获取订单数据:")
            print(json.dumps(orders, indent=2, ensure_ascii=False))
        else:
            print("获取订单数据失败")
            
    except Exception as e:
        print(f"程序执行出错: {e}")
