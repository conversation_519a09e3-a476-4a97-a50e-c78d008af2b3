import requests
import json


headers = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "zh-CN,zh;q=0.9",
    "content-type": "application/json;charset=UTF-8",
    "mallid": "634418218777560",
    "origin": "https://agentseller-us.temu.com",
    "priority": "u=1, i",
    "referer": "https://agentseller-us.temu.com/mmsos/waybill.html?logistics_create_time=1750003200000%2C1752681599999&page_number=2&page_size=200&sort_type=1&call_begin_time=1750003200000&call_end_time=1752681599999&active_waybill_tab=0",
    "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
    "x-document-referer": "https://agentseller-us.temu.com/main/authentication?redirectUrl=https%3A%2F%2Fagentseller-us.temu.com%2Fmmsos%2Fwaybill.html",
    "x-phan-data": "0aeJx7xMxiYPiIOSza0NzUyMzU3MDU1NzMWAfGszQwNjBB8AwNDUxjAT7WC4M"
}
cookies = {
    "api_uid": "CiCFY2h2Gge8HQBGEdGIAg==",
    "_nano_fp": "XpmyX5d8X0X8nqTxX9_JX7Kd7W6yX~XlC1RvKXhc",
    "_bee": "Guoq8zmpMw54MRLiHdZETgLhuZwVQapH",
    "njrpl": "Guoq8zmpMw54MRLiHdZETgLhuZwVQapH",
    "dilx": "8Y5zRfV0WSOxkuYUB-wow",
    "hfsc": "L3yPeow36T3/2p7PeQ==",
    "timezone": "Asia%2FShanghai",
    "webp": "1",
    "mallid": "634418218777560",
    "region": "0",
    "seller_temp": "N_eyJ0IjoiUU9oQ2pyS2FRUW9MenJrbkFzODNYVXRpdWJFZ2dVekU4bHA4UUtpemRxSEV2K2R4VzczS1lWWlFOV2tGUEVWR1U5WDl6K3V1QW1CZTdoQVQvQnVnYkE9PSIsInYiOjEsInMiOjEwMDAxLCJ1IjoyMzUxNDQ1MzE4NDUyMX0="
}
url = "https://agentseller-us.temu.com/mms/eagle/package/main_batch_query"
data = {
    "logistics_create_time": [
        1750003200000,
        1752681599999
    ],
    "page_number": 1,
    "page_size": 200,
    "sort_type": 1,
    "call_begin_time": 1750003200,
    "call_end_time": 1752681599
}
data = json.dumps(data, separators=(',', ':'))
response = requests.post(url, headers=headers, cookies=cookies, data=data)

print(response.text)
print(response)